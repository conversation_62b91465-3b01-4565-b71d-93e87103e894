const { PROVIDER_TYPE_CLAUDE_OFFICIAL, PROVIDER_TYPE_THIRD_PARTY } = require('./constants');

/**
 * 配置管理器
 * 直接使用VSCode配置API，无需临时文件通信
 */
class ConfigManager {
    constructor() {
        this.lastConfigHash = null;
        this.lastConfigCheck = 0;
        this.configurationSection = 'ccCopilot';
        this.vscode = null;

        // 尝试获取VSCode API
        this.initializeVSCodeAPI();
    }

    /**
     * 初始化VSCode API
     */
    initializeVSCodeAPI() {
        try {
            // 在Node.js环境中，VSCode API可能通过全局变量或require获得
            if (typeof require !== 'undefined') {
                try {
                    this.vscode = require('vscode');
                } catch (error) {
                    // VSCode API不可用，使用备用方案
                    console.warn('[DEBUG] [Claude Interceptor] ⚠️ VSCode API not available, using fallback');
                }
            }
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ⚠️ Failed to initialize VSCode API:', error.message);
        }
    }

    /**
     * 从VSCode获取设置（直接使用VSCode API）
     */
    loadSettingsFromStore() {
        try {
            console.log(`[DEBUG] [Claude Interceptor] 📞 Loading settings from VSCode...`);

            if (this.vscode && this.vscode.workspace) {
                // 直接使用VSCode API获取配置
                const config = this.vscode.workspace.getConfiguration(this.configurationSection);
                const settings = {
                    activeServiceProviderId: config.get('activeServiceProviderId', ''),
                    serviceProviders: config.get('serviceProviders', [])
                };

                console.log(`[DEBUG] [Claude Interceptor] ✅ Settings loaded from VSCode`);
                return settings;
            } else {
                // 备用方案：尝试从全局变量获取配置
                if (global.vscodeConfig && global.vscodeConfig[this.configurationSection]) {
                    console.log(`[DEBUG] [Claude Interceptor] ✅ Settings loaded from global config`);
                    return global.vscodeConfig[this.configurationSection];
                }

                console.warn(`[DEBUG] [Claude Interceptor] ⚠️ VSCode API not available, returning empty config`);
                return {
                    activeServiceProviderId: '',
                    serviceProviders: []
                };
            }
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ❌ Failed to load settings:', error.message);
            return {
                activeServiceProviderId: '',
                serviceProviders: []
            };
        }
    }

    /**
     * 保存设置到VSCode（直接使用VSCode API）
     */
    async saveSettingsToStore(settingsData) {
        try {
            console.log(`[DEBUG] [Claude Interceptor] 💾 Saving settings to VSCode...`);

            if (this.vscode && this.vscode.workspace) {
                // 直接使用VSCode API更新配置
                const config = this.vscode.workspace.getConfiguration(this.configurationSection);

                // 批量更新配置
                const updates = [];
                if (settingsData.activeServiceProviderId !== undefined) {
                    updates.push(config.update('activeServiceProviderId', settingsData.activeServiceProviderId, this.vscode.ConfigurationTarget.Global));
                }
                if (settingsData.serviceProviders !== undefined) {
                    updates.push(config.update('serviceProviders', settingsData.serviceProviders, this.vscode.ConfigurationTarget.Global));
                }

                await Promise.all(updates);
                console.log(`[DEBUG] [Claude Interceptor] ✅ Settings saved to VSCode`);
                return true;
            } else {
                // 备用方案：保存到全局变量
                if (!global.vscodeConfig) {
                    global.vscodeConfig = {};
                }
                global.vscodeConfig[this.configurationSection] = settingsData;
                console.log(`[DEBUG] [Claude Interceptor] ✅ Settings saved to global config`);
                return true;
            }
        } catch (error) {
            console.error('[DEBUG] [Claude Interceptor] ❌ Failed to save settings:', error.message);
            return false;
        }
    }

    /**
     * 从设置中获取当前活动账号
     */
    getCurrentActiveAccountFromSettings(settingsData) {
        try {
            console.log(`[DEBUG] [Claude Interceptor] 🔍 Getting active account from settings...`);
            
            // 直接从VSCode设置格式读取数据
            const activeServiceProviderId = settingsData.activeServiceProviderId;
            const serviceProviders = settingsData.serviceProviders || [];

            console.log(`[DEBUG] [Claude Interceptor] 📋 Active provider ID: ${activeServiceProviderId}`);
            console.log(`[DEBUG] [Claude Interceptor] 📦 Total providers: ${serviceProviders.length}`);

            const activeProvider = serviceProviders.find(p => p.id === activeServiceProviderId);
            if (!activeProvider || !activeProvider.activeAccountId) {
                console.log(`[DEBUG] [Claude Interceptor] ⚠️ No active provider or account ID found`);
                return null;
            }

            console.log(`[DEBUG] [Claude Interceptor] 🎯 Found active provider: ${activeProvider.type}, active account: ${activeProvider.activeAccountId}`);

            const account = activeProvider.accounts.find(acc => {
                if (activeProvider.type === PROVIDER_TYPE_CLAUDE_OFFICIAL) {
                    return acc.emailAddress === activeProvider.activeAccountId;
                } else {
                    return acc.id === activeProvider.activeAccountId;
                }
            });

            if (!account) {
                console.log(`[DEBUG] [Claude Interceptor] ⚠️ Active account not found in provider accounts`);
                return null;
            }

            console.log(`[DEBUG] [Claude Interceptor] ✅ Found active account: ${account.emailAddress || account.name}`);
            console.log(`[DEBUG] [Claude Interceptor] 🔑 Account has token: ${!!account.authorization}`);

            return { provider: activeProvider, account };
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ❌ Failed to get active account:', error.message);
            return null;
        }
    }

    /**
     * 判断当前服务提供方是否应该使用代理
     */
    shouldUseProxyForCurrentProvider(settingsData) {
        try {
            const activeResult = this.getCurrentActiveAccountFromSettings(settingsData);
            if (!activeResult) {
                return true; // 默认使用代理
            }
            return activeResult.provider.useProxy !== false; // 默认true
        } catch (error) {
            return true; // 默认使用代理
        }
    }

    /**
     * 简单哈希函数
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash;
    }

    /**
     * 设置配置变更监听器（使用VSCode API）
     */
    setupConfigWatcher(onConfigChanged) {
        try {
            if (this.vscode && this.vscode.workspace) {
                // 使用VSCode原生配置变更监听
                const disposable = this.vscode.workspace.onDidChangeConfiguration(event => {
                    if (event.affectsConfiguration(this.configurationSection)) {
                        console.log('[SILENT] [Claude Interceptor] Config changed, reloading configuration');
                        onConfigChanged();
                    }
                });

                console.log('[SILENT] [Claude Interceptor] VSCode config monitoring started');
                return disposable;
            } else {
                console.warn('[SILENT] [Claude Interceptor] VSCode API not available, config monitoring disabled');
                return null;
            }
        } catch (error) {
            console.warn('[SILENT] [Claude Interceptor] Failed to setup config monitoring:', error.message);
            return null;
        }
    }

    /**
     * 停止配置监听
     */
    stopConfigWatcher(watcher) {
        try {
            if (watcher && typeof watcher.dispose === 'function') {
                watcher.dispose();
                console.log('[SILENT] [Claude Interceptor] Config monitoring stopped');
            }
        } catch (error) {
            console.warn('[SILENT] [Claude Interceptor] Failed to cleanup monitor:', error.message);
        }
    }
}

module.exports = ConfigManager;