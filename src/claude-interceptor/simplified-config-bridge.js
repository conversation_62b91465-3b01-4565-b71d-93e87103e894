/**
 * 简化的配置桥接器
 * 为拦截器提供VSCode配置访问，无需临时文件通信
 */
class SimplifiedConfigBridge {
    constructor() {
        this.configurationSection = 'ccCopilot';
        this.configCache = null;
        this.configWatcher = null;
        this.changeCallbacks = new Set();
        
        // 初始化配置访问
        this.initializeConfigAccess();
    }

    /**
     * 初始化配置访问
     */
    initializeConfigAccess() {
        // 方案1: 通过全局变量访问VSCode配置（推荐）
        if (global.vscodeConfigBridge) {
            this.configBridge = global.vscodeConfigBridge;
            console.log('[DEBUG] [Claude Interceptor] ✅ Using global VSCode config bridge');
            return;
        }

        // 方案2: 尝试直接require VSCode API（在某些环境下可能工作）
        try {
            const vscode = require('vscode');
            if (vscode && vscode.workspace) {
                this.vscode = vscode;
                console.log('[DEBUG] [Claude Interceptor] ✅ Using direct VSCode API access');
                return;
            }
        } catch (error) {
            // VSCode API不可用，这是正常的
        }

        // 方案3: 使用配置缓存（备用方案）
        console.log('[DEBUG] [Claude Interceptor] ⚠️ Using config cache fallback');
    }

    /**
     * 获取配置数据
     */
    getConfig() {
        try {
            // 优先使用配置桥接器
            if (this.configBridge && typeof this.configBridge.getConfig === 'function') {
                const config = this.configBridge.getConfig(this.configurationSection);
                this.configCache = config;
                return config;
            }

            // 使用直接VSCode API
            if (this.vscode && this.vscode.workspace) {
                const vscodeConfig = this.vscode.workspace.getConfiguration(this.configurationSection);
                const config = {
                    activeServiceProviderId: vscodeConfig.get('activeServiceProviderId', ''),
                    serviceProviders: vscodeConfig.get('serviceProviders', [])
                };
                this.configCache = config;
                return config;
            }

            // 返回缓存的配置
            if (this.configCache) {
                return this.configCache;
            }

            // 返回默认配置
            return {
                activeServiceProviderId: '',
                serviceProviders: []
            };
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ❌ Failed to get config:', error.message);
            return this.configCache || {
                activeServiceProviderId: '',
                serviceProviders: []
            };
        }
    }

    /**
     * 更新配置数据
     */
    async updateConfig(updates) {
        try {
            // 优先使用配置桥接器
            if (this.configBridge && typeof this.configBridge.updateConfig === 'function') {
                await this.configBridge.updateConfig(this.configurationSection, updates);
                console.log('[DEBUG] [Claude Interceptor] ✅ Config updated via bridge');
                return true;
            }

            // 使用直接VSCode API
            if (this.vscode && this.vscode.workspace) {
                const config = this.vscode.workspace.getConfiguration(this.configurationSection);
                const updatePromises = [];
                
                for (const [key, value] of Object.entries(updates)) {
                    updatePromises.push(
                        config.update(key, value, this.vscode.ConfigurationTarget.Global)
                    );
                }
                
                await Promise.all(updatePromises);
                console.log('[DEBUG] [Claude Interceptor] ✅ Config updated via VSCode API');
                return true;
            }

            // 更新缓存
            this.configCache = { ...this.configCache, ...updates };
            console.log('[DEBUG] [Claude Interceptor] ⚠️ Config updated in cache only');
            return false;
        } catch (error) {
            console.error('[DEBUG] [Claude Interceptor] ❌ Failed to update config:', error.message);
            return false;
        }
    }

    /**
     * 监听配置变更
     */
    onConfigChange(callback) {
        this.changeCallbacks.add(callback);

        // 设置监听器（如果还没有）
        if (!this.configWatcher) {
            this.setupConfigWatcher();
        }

        // 返回取消监听的函数
        return () => {
            this.changeCallbacks.delete(callback);
            if (this.changeCallbacks.size === 0 && this.configWatcher) {
                this.stopConfigWatcher();
            }
        };
    }

    /**
     * 设置配置监听器
     */
    setupConfigWatcher() {
        try {
            // 优先使用配置桥接器
            if (this.configBridge && typeof this.configBridge.onConfigChange === 'function') {
                this.configWatcher = this.configBridge.onConfigChange(this.configurationSection, () => {
                    this.notifyConfigChange();
                });
                console.log('[DEBUG] [Claude Interceptor] ✅ Config watcher setup via bridge');
                return;
            }

            // 使用直接VSCode API
            if (this.vscode && this.vscode.workspace) {
                this.configWatcher = this.vscode.workspace.onDidChangeConfiguration(event => {
                    if (event.affectsConfiguration(this.configurationSection)) {
                        this.notifyConfigChange();
                    }
                });
                console.log('[DEBUG] [Claude Interceptor] ✅ Config watcher setup via VSCode API');
                return;
            }

            console.log('[DEBUG] [Claude Interceptor] ⚠️ Config watcher not available');
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ❌ Failed to setup config watcher:', error.message);
        }
    }

    /**
     * 停止配置监听器
     */
    stopConfigWatcher() {
        try {
            if (this.configWatcher) {
                if (typeof this.configWatcher.dispose === 'function') {
                    this.configWatcher.dispose();
                } else if (typeof this.configWatcher === 'function') {
                    this.configWatcher();
                }
                this.configWatcher = null;
                console.log('[DEBUG] [Claude Interceptor] ✅ Config watcher stopped');
            }
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ❌ Failed to stop config watcher:', error.message);
        }
    }

    /**
     * 通知配置变更
     */
    notifyConfigChange() {
        try {
            // 清除缓存，强制重新获取
            this.configCache = null;
            
            // 通知所有监听器
            for (const callback of this.changeCallbacks) {
                try {
                    callback();
                } catch (error) {
                    console.warn('[DEBUG] [Claude Interceptor] ❌ Config change callback error:', error.message);
                }
            }
        } catch (error) {
            console.warn('[DEBUG] [Claude Interceptor] ❌ Failed to notify config change:', error.message);
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.stopConfigWatcher();
        this.changeCallbacks.clear();
        this.configCache = null;
    }
}

module.exports = SimplifiedConfigBridge;
