import * as vscode from 'vscode'

/**
 * VSCode配置桥接器
 * 为拦截器进程提供VSCode配置访问能力
 * 通过全局变量暴露配置接口，避免临时文件通信
 */
export class VSCodeConfigBridge {
  private disposables: vscode.Disposable[] = []
  private changeListeners: Map<string, Set<() => void>> = new Map()

  constructor(private context: vscode.ExtensionContext) {
    this.setupGlobalBridge()
  }

  /**
   * 设置全局配置桥接器
   */
  private setupGlobalBridge(): void {
    // 将配置桥接器暴露到全局变量
    ;(global as any).vscodeConfigBridge = {
      getConfig: this.getConfig.bind(this),
      updateConfig: this.updateConfig.bind(this),
      onConfigChange: this.onConfigChange.bind(this)
    }

    console.log('[DEBUG] [VSCode Config Bridge] ✅ Global config bridge established')
  }

  /**
   * 获取配置
   */
  getConfig(section: string): any {
    try {
      const config = vscode.workspace.getConfiguration(section)
      
      // 返回所有配置项
      const result: any = {}
      const inspect = config.inspect('')
      
      if (inspect) {
        // 获取所有已定义的配置键
        const keys = Object.keys(inspect.defaultValue || {})
        for (const key of keys) {
          result[key] = config.get(key)
        }
      }
      
      // 手动添加已知的配置项（确保不遗漏）
      result.activeServiceProviderId = config.get('activeServiceProviderId', '')
      result.serviceProviders = config.get('serviceProviders', [])
      result.proxyConfig = config.get('proxyConfig', {})
      
      return result
    } catch (error) {
      console.error('[DEBUG] [VSCode Config Bridge] ❌ Failed to get config:', error)
      return {}
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(section: string, updates: Record<string, any>): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration(section)
      const updatePromises: Promise<void>[] = []
      
      for (const [key, value] of Object.entries(updates)) {
        updatePromises.push(
          config.update(key, value, vscode.ConfigurationTarget.Global)
        )
      }
      
      await Promise.all(updatePromises)
      console.log('[DEBUG] [VSCode Config Bridge] ✅ Config updated:', Object.keys(updates))
    } catch (error) {
      console.error('[DEBUG] [VSCode Config Bridge] ❌ Failed to update config:', error)
      throw error
    }
  }

  /**
   * 监听配置变更
   */
  onConfigChange(section: string, callback: () => void): () => void {
    // 添加到监听器集合
    if (!this.changeListeners.has(section)) {
      this.changeListeners.set(section, new Set())
    }
    this.changeListeners.get(section)!.add(callback)

    // 如果是第一个监听器，设置VSCode配置监听
    if (this.changeListeners.get(section)!.size === 1) {
      this.setupVSCodeConfigListener(section)
    }

    // 返回取消监听的函数
    return () => {
      const listeners = this.changeListeners.get(section)
      if (listeners) {
        listeners.delete(callback)
        if (listeners.size === 0) {
          this.changeListeners.delete(section)
          // 清理VSCode监听器会在dispose时统一处理
        }
      }
    }
  }

  /**
   * 设置VSCode配置监听器
   */
  private setupVSCodeConfigListener(section: string): void {
    const disposable = vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration(section)) {
        const listeners = this.changeListeners.get(section)
        if (listeners) {
          for (const callback of listeners) {
            try {
              callback()
            } catch (error) {
              console.error('[DEBUG] [VSCode Config Bridge] ❌ Config change callback error:', error)
            }
          }
        }
      }
    })

    this.disposables.push(disposable)
    console.log(`[DEBUG] [VSCode Config Bridge] ✅ Config listener setup for section: ${section}`)
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 清理所有VSCode监听器
    for (const disposable of this.disposables) {
      disposable.dispose()
    }
    this.disposables = []

    // 清理监听器集合
    this.changeListeners.clear()

    // 清理全局变量
    delete (global as any).vscodeConfigBridge

    console.log('[DEBUG] [VSCode Config Bridge] ✅ Config bridge disposed')
  }
}

/**
 * 创建并初始化VSCode配置桥接器
 */
export function createVSCodeConfigBridge(context: vscode.ExtensionContext): VSCodeConfigBridge {
  const bridge = new VSCodeConfigBridge(context)
  
  // 注册到扩展上下文，确保在扩展停用时清理
  context.subscriptions.push({
    dispose: () => bridge.dispose()
  })
  
  return bridge
}
