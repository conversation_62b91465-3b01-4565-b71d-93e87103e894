import * as vscode from 'vscode'
import { EventEmitter } from 'events'
import { AppSettings, ServiceProvider, ClaudeAccount, ThirdPartyAccount } from './types'

/**
 * 统一设置管理器
 * 负责管理整个插件的所有配置项目，包括账号信息
 * 提供统一的配置读写接口和变更通知
 */
export class SettingsManager extends EventEmitter {
  private context: vscode.ExtensionContext
  private readonly configurationSection = 'ccCopilot'
  private configChangeListener: vscode.Disposable | null = null

  constructor(context: vscode.ExtensionContext) {
    super()
    this.context = context
    this.setupConfigurationListener()
  }

  // ==================== 核心配置管理 ====================

  /**
   * 获取完整的应用设置
   */
  getSettings(): AppSettings {
    const config = vscode.workspace.getConfiguration(this.configurationSection)
    
    return {
      proxyConfig: config.get('proxyConfig', {}),
      apiProviders: config.get('apiProviders', []),
      activeProviderId: config.get('activeProviderId', ''),
      serviceProviders: config.get('serviceProviders', []),
      activeServiceProviderId: config.get('activeServiceProviderId', '')
    }
  }

  /**
   * 更新配置项
   */
  async updateSetting<T>(key: string, value: T): Promise<void> {
    const config = vscode.workspace.getConfiguration(this.configurationSection)
    await config.update(key, value, vscode.ConfigurationTarget.Global)
    this.emit('setting:updated', { key, value })
  }

  /**
   * 批量更新配置
   */
  async updateSettings(updates: Record<string, any>): Promise<void> {
    const config = vscode.workspace.getConfiguration(this.configurationSection)
    
    for (const [key, value] of Object.entries(updates)) {
      await config.update(key, value, vscode.ConfigurationTarget.Global)
    }
    
    this.emit('settings:batch-updated', updates)
  }

  // ==================== 服务提供商管理 ====================

  /**
   * 获取所有服务提供商
   */
  getServiceProviders(): ServiceProvider[] {
    return this.getSettings().serviceProviders
  }

  /**
   * 添加或更新服务提供商
   */
  async addOrUpdateServiceProvider(provider: ServiceProvider): Promise<void> {
    const providers = this.getServiceProviders()
    const existingIndex = providers.findIndex(p => p.id === provider.id)

    if (existingIndex >= 0) {
      providers[existingIndex] = provider
    } else {
      providers.push(provider)
    }

    await this.updateSetting('serviceProviders', providers)
    this.emit('service-providers:updated', providers)
  }

  /**
   * 删除服务提供商
   */
  async removeServiceProvider(providerId: string): Promise<void> {
    const providers = this.getServiceProviders().filter(p => p.id !== providerId)
    const currentActiveId = this.getSettings().activeServiceProviderId
    
    const updates: Record<string, any> = {
      serviceProviders: providers
    }
    
    if (currentActiveId === providerId) {
      updates.activeServiceProviderId = ''
    }
    
    await this.updateSettings(updates)
    this.emit('service-providers:updated', providers)
  }

  /**
   * 获取活动的服务提供商
   */
  getActiveServiceProvider(): ServiceProvider | null {
    const settings = this.getSettings()
    const providers = settings.serviceProviders
    return providers.find(p => p.id === settings.activeServiceProviderId) || null
  }

  /**
   * 设置活动的服务提供商
   */
  async setActiveServiceProvider(providerId: string): Promise<void> {
    await this.updateSetting('activeServiceProviderId', providerId)
    this.emit('active-service-provider:changed', providerId)
  }

  // ==================== 账号管理 ====================

  /**
   * 获取当前活动账号
   */
  getCurrentActiveAccount(): { provider: ServiceProvider, account: ClaudeAccount | ThirdPartyAccount } | null {
    const activeProvider = this.getActiveServiceProvider()
    if (!activeProvider || !activeProvider.activeAccountId) {
      return null
    }

    const account = activeProvider.accounts.find(acc => {
      if (activeProvider.type === 'claude_official') {
        return (acc as ClaudeAccount).emailAddress === activeProvider.activeAccountId
      } else {
        return (acc as ThirdPartyAccount).id === activeProvider.activeAccountId
      }
    })

    return account ? { provider: activeProvider, account } : null
  }

  /**
   * 添加或更新账号
   */
  async addOrUpdateAccount(providerId: string, account: ClaudeAccount | ThirdPartyAccount): Promise<void> {
    const providers = this.getServiceProviders()
    const provider = providers.find(p => p.id === providerId)
    
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`)
    }

    const accounts = provider.accounts || []
    const accountId = (account as ClaudeAccount).emailAddress || (account as ThirdPartyAccount).id
    const existingIndex = accounts.findIndex(acc => {
      if (provider.type === 'claude_official') {
        return (acc as ClaudeAccount).emailAddress === accountId
      } else {
        return (acc as ThirdPartyAccount).id === accountId
      }
    })

    if (existingIndex >= 0) {
      accounts[existingIndex] = account
    } else {
      accounts.push(account)
    }

    provider.accounts = accounts
    await this.addOrUpdateServiceProvider(provider)
    this.emit('account:updated', { providerId, account })
  }

  /**
   * 设置活动账号
   */
  async setActiveAccount(providerId: string, accountId: string): Promise<void> {
    const providers = this.getServiceProviders()
    const provider = providers.find(p => p.id === providerId)
    
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`)
    }

    provider.activeAccountId = accountId
    
    const updates: Record<string, any> = {
      serviceProviders: providers,
      activeServiceProviderId: providerId
    }
    
    await this.updateSettings(updates)
    this.emit('active-account:changed', { providerId, accountId })
  }

  // ==================== 配置监听 ====================

  /**
   * 设置配置变更监听器
   */
  private setupConfigurationListener(): void {
    this.configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration(this.configurationSection)) {
        this.emit('configuration:changed', event)
      }
    })
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.configChangeListener) {
      this.configChangeListener.dispose()
      this.configChangeListener = null
    }
    this.removeAllListeners()
  }
}
