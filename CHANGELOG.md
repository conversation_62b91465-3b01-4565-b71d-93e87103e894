# Change Log

All notable changes to the "CC Copilot" extension will be documented in this file.

## [0.1.0] - 2024-08-03

### Added
- 🎉 Initial release of CC Copilot extension
- 📊 Session management with graphical interface
- 🔄 Multi-account support for Claude and third-party AI providers
- 🌲 Tree view integration in VS Code sidebar
- 💻 Terminal integration for Claude CLI sessions
- 🔄 Auto-sync with existing Claude sessions
- 📁 Workspace integration with project prioritization
- ⚙️ Configuration management for API keys and settings
- 🔍 Advanced monitoring with request interception
- 🌐 Proxy support for network restrictions
- 🔌 Third-party API provider support
- 🚀 Easy account switching and provider management
- 📖 Comprehensive documentation in English and Chinese

### Features
- Session creation and management
- Claude CLI integration
- Multiple AI service provider support
- Proxy configuration
- Account discovery and management
- Project-based session organization
- Terminal integration
- Settings management

---

## English

### What's New in v0.1.0
This is the initial release of CC Copilot, bringing a comprehensive graphical interface for managing Claude Code CLI sessions directly within VS Code.

**Key Highlights:**
- Seamless integration with Claude CLI
- Support for multiple AI providers
- Intuitive sidebar interface
- Advanced configuration options

---

## 中文

### v0.1.0 新功能
这是 CC Copilot 的首次发布，为 VS Code 带来了全面的图形界面来管理 Claude Code CLI 会话。

**主要亮点：**
- 与 Claude CLI 无缝集成
- 支持多个 AI 提供商
- 直观的侧边栏界面
- 高级配置选项