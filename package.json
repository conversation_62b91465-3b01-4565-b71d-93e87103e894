{"name": "cc-copilot", "displayName": "CC Copilot", "description": "A VS Code extension for managing Claude Code CLI sessions and quickly switching between AI accounts", "version": "0.1.0", "publisher": "cc-copilot", "author": {"name": "CC Copilot Team"}, "license": "MIT", "homepage": "https://github.com/your-username/cc-copilot-vs-code#readme", "repository": {"type": "git", "url": "https://github.com/your-username/cc-copilot-vs-code.git"}, "bugs": {"url": "https://github.com/your-username/cc-copilot-vs-code/issues"}, "icon": "src/cc-copilot-icon1024.png", "engines": {"vscode": "^1.74.0"}, "categories": ["Machine Learning", "Other"], "keywords": ["claude", "ai", "artificial intelligence", "terminal", "session", "companion", "copilot", "anthropic", "code assistant", "third party ai"], "galleryBanner": {"color": "#1e1e1e", "theme": "dark"}, "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "cc-copilot-sidebar", "title": "CC Copilot", "icon": "$(robot)"}]}, "views": {"cc-copilot-sidebar": [{"id": "claude-sessions-view", "name": "Sessions", "type": "tree", "icon": "$(list-unordered)"}]}, "commands": [{"command": "cc-copilot.newSession", "title": "New <PERSON>", "icon": "$(add)"}, {"command": "cc-copilot.refreshSessions", "title": "Refresh Sessions", "icon": "$(refresh)"}, {"command": "cc-copilot.openSession", "title": "Open Session"}, {"command": "cc-copilot.deleteSession", "title": "Delete Session", "icon": "$(trash)"}, {"command": "cc-copilot.openSettings", "title": "Open Settings", "icon": "$(settings-gear)"}, {"command": "cc-copilot.addThirdPartyProvider", "title": "Add Third Party AI Provider", "icon": "$(add)"}, {"command": "cc-copilot.selectActiveProvider", "title": "Select Active AI Provider", "icon": "$(account)"}, {"command": "cc-copilot.discoverClaudeAccounts", "title": "Discover <PERSON> Accounts", "icon": "$(search)"}, {"command": "cc-copilot.claude<PERSON><PERSON>in", "title": "Login to <PERSON>", "icon": "$(sign-in)"}, {"command": "cc-copilot.reloginAccount", "title": "Re-login Account", "icon": "$(refresh)"}, {"command": "cc-copilot.showMoreActions", "title": "More Actions", "icon": "$(kebab-vertical)"}, {"command": "cc-copilot.newSessionForProject", "title": "New Session for Project", "icon": "$(add)"}, {"command": "cc-copilot.showAccountMenu", "title": "Account <PERSON><PERSON>", "icon": "$(ellipsis)"}, {"command": "cc-copilot.addAccount", "title": "Add Account", "icon": "$(add)"}, {"command": "cc-copilot.refreshAccounts", "title": "Refresh Accounts", "icon": "$(refresh)"}, {"command": "cc-copilot.selectAccount", "title": "Select Account"}], "menus": {"view/title": [{"command": "cc-copilot.showMoreActions", "when": "view == claude-sessions-view", "group": "navigation"}], "view/item/context": [{"command": "cc-copilot.newSessionForProject", "when": "view == claude-sessions-view && viewItem == project", "group": "inline"}, {"command": "cc-copilot.newSessionForProject", "when": "view == claude-sessions-view && viewItem == project", "group": "context@1"}, {"command": "cc-copilot.refreshSessions", "when": "view == claude-sessions-view && viewItem == project", "group": "context@2"}, {"command": "cc-copilot.openSession", "when": "view == claude-sessions-view && viewItem == session", "group": "inline"}, {"command": "cc-copilot.deleteSession", "when": "view == claude-sessions-view && viewItem == session", "group": "context"}, {"command": "cc-copilot.selectActiveProvider", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "inline@1"}, {"command": "cc-copilot.addAccount", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "inline@2"}, {"command": "cc-copilot.showAccountMenu", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "inline@3"}, {"command": "cc-copilot.addThirdPartyProvider", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "context@1"}, {"command": "cc-copilot.openSettings", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "context@2"}, {"command": "cc-copilot.discoverClaudeAccounts", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "context@3"}, {"command": "cc-copilot.claude<PERSON><PERSON>in", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "context@4"}, {"command": "cc-copilot.reloginAccount", "when": "view == claude-sessions-view && viewItem == accountSelector", "group": "context@5"}, {"command": "cc-copilot.refreshAccounts", "when": "view == claude-sessions-view && viewItem == accountManagement", "group": "inline"}]}, "configuration": {"title": "CC Copilot", "properties": {"ccCopilot.proxyConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false, "description": "Enable proxy for API requests"}, "url": {"type": "string", "default": "http://127.0.0.1:7890", "format": "uri", "pattern": "^https?://.*", "patternErrorMessage": "Must be a valid HTTP or HTTPS URL", "description": "Proxy server URL"}, "username": {"type": "string", "default": "", "description": "Proxy authentication username (optional)"}, "password": {"type": "string", "default": "", "description": "Proxy authentication password (optional)"}}, "additionalProperties": false, "default": {"enabled": false, "url": "http://127.0.0.1:7890", "username": "", "password": ""}, "markdownDescription": "**Proxy configuration** for Claude API requests. Enable to route requests through a proxy server."}, "ccCopilot.serviceProviders": {"type": "array", "default": [], "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the service provider"}, "type": {"type": "string", "enum": ["claude_official", "third_party"], "description": "Type of service provider"}, "name": {"type": "string", "description": "Display name for the service provider"}, "accounts": {"type": "array", "description": "List of accounts for this provider", "items": {"oneOf": [{"type": "object", "title": "Third Party Account", "properties": {"id": {"type": "string", "description": "Account unique identifier"}, "name": {"type": "string", "description": "Account display name"}, "apiKey": {"type": "string", "description": "API key for authentication"}, "baseUrl": {"type": "string", "format": "uri", "description": "Base URL for the API endpoint"}, "description": {"type": "string", "description": "Optional description for the account"}}, "required": ["id", "name", "<PERSON><PERSON><PERSON><PERSON>", "baseUrl"], "additionalProperties": false}, {"type": "object", "title": "Claude Official Account", "properties": {"accountUuid": {"type": "string"}, "emailAddress": {"type": "string"}, "organizationUuid": {"type": "string"}, "organizationRole": {"type": "string"}, "workspaceRole": {"type": ["string", "null"]}, "organizationName": {"type": "string"}, "authorization": {"type": "string"}}, "required": ["accountUuid", "emailAddress", "organizationUuid", "organizationRole", "organizationName"], "additionalProperties": false}]}}, "activeAccountId": {"type": "string", "description": "ID of the currently active account"}, "useProxy": {"type": "boolean", "default": true, "description": "Whether to use proxy configuration for this provider"}}, "required": ["id", "type", "name", "accounts", "activeAccountId", "useProxy"], "additionalProperties": false}, "markdownDescription": "**Service providers configuration**\n\nManage multiple AI service providers including Claude Official and third-party APIs. Each provider can have multiple accounts."}, "ccCopilot.activeServiceProviderId": {"type": "string", "default": "", "markdownDescription": "**Active service provider ID**\n\nThe currently active AI service provider. Use the command palette (`Ctrl+Shift+P` / `Cmd+Shift+P`) and search for \"Select Active AI Provider\" to choose from available providers, or use the provider selection button in the CC Copilot sidebar."}, "ccCopilot.defaultProviderType": {"type": "string", "enum": ["claude_official", "third_party"], "enumItemLabels": ["<PERSON>", "Third Party API"], "enumDescriptions": ["Use official Claude.ai account with browser authentication", "Use third-party API service with custom API keys"], "default": "claude_official", "markdownDescription": "**Default service provider type** for new configurations"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "typescript": "^4.9.4"}, "dependencies": {"@types/uuid": "^9.0.8", "uuid": "^9.0.1"}}